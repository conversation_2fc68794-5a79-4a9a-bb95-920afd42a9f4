"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppModule = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const typeorm_1 = require("@nestjs/typeorm");
const passport_1 = require("@nestjs/passport");
const jwt_1 = require("@nestjs/jwt");
const auth_module_1 = require("./auth/auth.module");
const users_module_1 = require("./users/users.module");
const stores_module_1 = require("./stores/stores.module");
const products_module_1 = require("./products/products.module");
const customers_module_1 = require("./customers/customers.module");
const orders_module_1 = require("./orders/orders.module");
const conversations_module_1 = require("./conversations/conversations.module");
const agents_module_1 = require("./agents/agents.module");
const images_module_1 = require("./images/images.module");
const user_entity_1 = require("./users/user.entity");
const store_entity_1 = require("./stores/store.entity");
const customer_entity_1 = require("./customers/customer.entity");
const product_entity_1 = require("./products/product.entity");
const order_entity_1 = require("./orders/order.entity");
const order_item_entity_1 = require("./orders/order-item.entity");
const conversation_entity_1 = require("./conversations/conversation.entity");
const message_entity_1 = require("./conversations/message.entity");
const tool_call_entity_1 = require("./conversations/tool-call.entity");
const agent_entity_1 = require("./agents/agent.entity");
const account_entity_1 = require("./auth/account.entity");
const session_entity_1 = require("./auth/session.entity");
const verification_token_entity_1 = require("./auth/verification-token.entity");
const post_entity_1 = require("./auth/post.entity");
const app_controller_1 = require("./app.controller");
const app_service_1 = require("./app.service");
let AppModule = class AppModule {
};
exports.AppModule = AppModule;
exports.AppModule = AppModule = __decorate([
    (0, common_1.Module)({
        imports: [
            config_1.ConfigModule.forRoot({
                isGlobal: true,
                envFilePath: '.env',
            }),
            typeorm_1.TypeOrmModule.forRoot({
                type: 'postgres',
                host: process.env.DB_HOST || 'localhost',
                port: parseInt(process.env.DB_PORT || '5432'),
                username: process.env.DB_USER || 'postgres',
                password: process.env.DB_PASSWORD || '',
                database: process.env.DB_NAME || 'teno_store',
                synchronize: false,
                logging: false,
                entities: [
                    user_entity_1.User,
                    store_entity_1.Store,
                    customer_entity_1.Customer,
                    product_entity_1.Product,
                    order_entity_1.Order,
                    order_item_entity_1.OrderItem,
                    conversation_entity_1.Conversation,
                    message_entity_1.Message,
                    tool_call_entity_1.ToolCall,
                    agent_entity_1.Agent,
                    account_entity_1.Account,
                    session_entity_1.Session,
                    verification_token_entity_1.VerificationToken,
                    post_entity_1.Post
                ],
                ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,
                extra: {
                    max: 20,
                    connectionTimeoutMillis: 10000,
                    idleTimeoutMillis: 30000,
                },
            }),
            passport_1.PassportModule.register({ defaultStrategy: 'jwt' }),
            jwt_1.JwtModule.register({
                secret: process.env.JWT_SECRET || 'your-secret-key',
                signOptions: { expiresIn: '24h' },
            }),
            auth_module_1.AuthModule,
            users_module_1.UsersModule,
            stores_module_1.StoresModule,
            products_module_1.ProductsModule,
            customers_module_1.CustomersModule,
            orders_module_1.OrdersModule,
            conversations_module_1.ConversationsModule,
            agents_module_1.AgentsModule,
            images_module_1.ImagesModule,
        ],
        controllers: [app_controller_1.AppController],
        providers: [app_service_1.AppService],
    })
], AppModule);
//# sourceMappingURL=app.module.js.map