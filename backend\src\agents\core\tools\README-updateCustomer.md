# Update Customer Tool

## Overview
The `updateCustomer` tool allows AI agents to update customer information when new details become available during conversations. This is particularly useful for maintaining accurate customer records and improving the quality of customer service.

## Functionality
- Updates customer phone numbers, addresses, emails, and names
- Only updates fields that are provided (delta-like behavior)
- Automatically updates conversation context with the updated customer information
- Validates that the customer exists and belongs to the current store
- Maintains audit trail with `updatedBy` and `updatedAt` fields

## Parameters

### Required
- `customerId` (string): The ID of the customer to update

### Optional
- `phone` (string): New phone number
- `address` (string): New address
- `email` (string): New email address
- `name` (string): New customer name

## Usage Examples

### Update Phone Number
```typescript
updateCustomer({
  customerId: "123",
  phone: "******-0123"
})
```

### Update Address
```typescript
updateCustomer({
  customerId: "123",
  address: "123 Main St, Anytown, ST 12345"
})
```

### Update Multiple Fields
```typescript
updateCustomer({
  customerId: "123",
  phone: "******-0123",
  address: "123 Main St, Anytown, ST 12345",
  email: "<EMAIL>"
})
```

## Return Value
The tool returns an object containing:
- `message`: Success message
- `customer`: Updated customer information
- `updatedFields`: Array of field names that were updated

## Backend Support
The backend already includes all necessary components:
- ✅ Database schema with `address` field
- ✅ Service layer with `updateCustomer` function
- ✅ API endpoint: `PUT /customers/{id}`
- ✅ Input validation with Zod schemas
- ✅ Soft delete support and audit trails

## Security Features
- Validates customer belongs to the current store
- Requires valid conversation context
- Updates are tracked with user ID and timestamp
- No sensitive data exposure in responses

## Integration
This tool is automatically available to AI agents through the conversation tools builder and integrates seamlessly with the existing customer management system.
