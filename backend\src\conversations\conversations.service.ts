import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository, InjectDataSource } from '@nestjs/typeorm';
import { Repository, DataSource } from 'typeorm';
import { Conversation } from './conversation.entity';
import { Message } from './message.entity';
import { ToolCall } from './tool-call.entity';
import { generateUUID7 } from '../utils/uuid';
import { processUserMessage } from '../agents/core/conversation.service';

@Injectable()
export class ConversationsService {
  constructor(
    @InjectRepository(Conversation)
    private conversationsRepository: Repository<Conversation>,
    @InjectRepository(Message)
    private messagesRepository: Repository<Message>,
    @InjectRepository(ToolCall)
    private toolCallsRepository: Repository<ToolCall>,
    @InjectDataSource()
    private dataSource: DataSource,
  ) {}

  async findAll(): Promise<Conversation[]> {
    return this.conversationsRepository.find({
      where: { isDeleted: false },
      order: { createdAt: 'DESC' },
    });
  }

  async findOne(id: string): Promise<Conversation> {
    const conversation = await this.conversationsRepository.findOne({
      where: { id, isDeleted: false },
      relations: ['messages', 'toolCalls'],
    });

    if (!conversation) {
      throw new NotFoundException(`Conversation with ID ${id} not found`);
    }

    return conversation;
  }

  async findByStoreId(storeId: string): Promise<Conversation[]> {
    return this.conversationsRepository.find({
      where: { storeId, isDeleted: false },
      order: { createdAt: 'DESC' },
    });
  }

  async findByUserId(userId: string): Promise<Conversation[]> {
    return this.conversationsRepository.find({
      where: { userId, isDeleted: false },
      order: { createdAt: 'DESC' },
    });
  }

  async findByUuid(uuid: string): Promise<Conversation> {
    const conversation = await this.conversationsRepository.findOne({
      where: { uuid, isDeleted: false },
      relations: ['messages', 'toolCalls'],
    });

    if (!conversation) {
      throw new NotFoundException(`Conversation with UUID ${uuid} not found`);
    }

    return conversation;
  }

  async create(createConversationDto: Partial<Conversation> & { userId?: string | number; storeId?: string | number; createdBy?: string | number }): Promise<Conversation> {
    // Extract userId and map it to the correct fields
    const { userId, storeId, createdBy, ...conversationData } = createConversationDto;

    console.log('Creating conversation with data:', {
      userId,
      storeId,
      createdBy,
      conversationData,
      userIdString: userId?.toString(),
      storeIdString: storeId?.toString()
    });

    // Create the conversation with proper field mapping and auto-generated UUID
    const conversation = this.conversationsRepository.create({
      ...conversationData,
      uuid: generateUUID7(), // Auto-generate UUID7 for the conversation
      userId: userId?.toString(), // Map userId to the entity field
      storeId: storeId?.toString(), // Ensure storeId is properly mapped
      createdBy: (createdBy || userId)?.toString(), // Use createdBy if provided, otherwise use userId
    });

    console.log('Created conversation object:', conversation);

    return this.conversationsRepository.save(conversation);
  }

  async update(id: string, updateConversationDto: Partial<Conversation>): Promise<Conversation> {
    const conversation = await this.findOne(id);
    Object.assign(conversation, updateConversationDto);
    return this.conversationsRepository.save(conversation);
  }

  async remove(id: string): Promise<void> {
    const conversation = await this.findOne(id);
    conversation.isDeleted = true;
    await this.conversationsRepository.save(conversation);
  }

  async getUnifiedTimeline(id: string, page: number = 1, limit: number = 50): Promise<any> {
    const conversation = await this.findOne(id);

    // Get messages for this conversation
    const messages = await this.messagesRepository.find({
      where: {
        conversationId: id,
        isDeleted: false
      },
      order: { createdAt: 'ASC' },
    });

    // Get tool calls for this conversation
    const toolCalls = await this.toolCallsRepository.find({
      where: {
        conversationId: id,
        isDeleted: false
      },
      order: { createdAt: 'ASC' },
    });

    // Combine messages and tool calls into a unified timeline
    const timelineItems = [
      ...messages.map(message => ({
        id: message.id,
        type: 'message',
        content: message.content,
        role: message.role,
        metadata: message.metadata,
        createdAt: message.createdAt,
        updatedAt: message.updatedAt,
        userId: message.userId,
        conversationId: message.conversationId,
      })),
      ...toolCalls.map(toolCall => ({
        id: toolCall.id,
        type: 'tool_call',
        toolName: toolCall.toolName,
        parameters: toolCall.parameters,
        result: toolCall.result,
        status: toolCall.status,
        error: toolCall.error,
        duration: toolCall.duration,
        createdAt: toolCall.createdAt,
        updatedAt: toolCall.updatedAt,
        userId: toolCall.userId,
        conversationId: toolCall.conversationId,
      }))
    ];

    // Sort by creation time
    timelineItems.sort((a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime());

    // Apply pagination
    const total = timelineItems.length;
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const timeline = timelineItems.slice(startIndex, endIndex);

    return {
      timeline,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  async getUnifiedTimelineByUuid(uuid: string, page: number = 1, limit: number = 50): Promise<any> {
    const conversation = await this.findByUuid(uuid);
    return this.getUnifiedTimeline(conversation.id, page, limit);
  }

  async getTimeline(id: string, page: number = 1, limit: number = 50): Promise<any> {
    // For now, timeline and unified timeline are the same
    // In the future, timeline might include additional events like tool calls, status changes, etc.
    return this.getUnifiedTimeline(id, page, limit);
  }

  async addMessage(conversationId: string, messageData: Partial<Message> & {
    content: string;
    createdBy: string;
    agentId?: string;
    userId?: string;
    customerId?: string;
    imageUrl?: string;
    videoUrl?: string;
    attachmentUrl?: string;
    attachmentType?: string;
    cost?: number;
    executionTime?: number;
    inputTokens?: number;
    outputTokens?: number;
  }): Promise<Message> {
    // Verify conversation exists
    const conversation = await this.findOne(conversationId);

    // Determine role based on the message data
    let role = 'user'; // default role
    if (messageData.agentId) {
      role = 'assistant';
    } else if (messageData.customerId) {
      role = 'user';
    }

    // Create metadata object from additional fields
    const metadata: any = {};
    if (messageData.agentId) metadata.agentId = messageData.agentId;
    if (messageData.customerId) metadata.customerId = messageData.customerId;
    if (messageData.imageUrl) metadata.imageUrl = messageData.imageUrl;
    if (messageData.videoUrl) metadata.videoUrl = messageData.videoUrl;
    if (messageData.attachmentUrl) metadata.attachmentUrl = messageData.attachmentUrl;
    if (messageData.attachmentType) metadata.attachmentType = messageData.attachmentType;
    if (messageData.cost) metadata.cost = messageData.cost;
    if (messageData.executionTime) metadata.executionTime = messageData.executionTime;
    if (messageData.inputTokens) metadata.inputTokens = messageData.inputTokens;
    if (messageData.outputTokens) metadata.outputTokens = messageData.outputTokens;

    // Create the message
    const message = this.messagesRepository.create({
      content: messageData.content,
      role,
      metadata: Object.keys(metadata).length > 0 ? metadata : null,
      conversationId,
      userId: messageData.userId || messageData.createdBy,
      createdBy: messageData.createdBy,
    });

    return this.messagesRepository.save(message);
  }

  async addMessageByUuid(uuid: string, messageData: Partial<Message> & {
    content: string;
    createdBy: string;
    agentId?: string;
    userId?: string;
    customerId?: string;
    imageUrl?: string;
    videoUrl?: string;
    attachmentUrl?: string;
    attachmentType?: string;
    cost?: number;
    executionTime?: number;
    inputTokens?: number;
    outputTokens?: number;
  }): Promise<Message> {
    // Find conversation by UUID
    const conversation = await this.findByUuid(uuid);

    // Use the conversation ID to add the message
    const savedMessage = await this.addMessage(conversation.id, messageData);

    // Check if this is a user message (not from agent) and trigger agent response
    const isUserMessage = !messageData.agentId || messageData.agentId === 'customer-message';

    if (isUserMessage && messageData.content) {
      try {
        // Extract the actual message content (remove customer name prefix if present)
        let userMessageContent = messageData.content;
        const customerNameMatch = messageData.content.match(/^\[([^\]]+)\]:\s*(.*)$/);
        if (customerNameMatch) {
          userMessageContent = customerNameMatch[2];
        }

        // Trigger agent response asynchronously (don't wait for it)
        setImmediate(async () => {
          try {
            await processUserMessage(
              this.dataSource,
              {
                conversationUuid: uuid,
                userMessage: userMessageContent,
                ownerUserId: messageData.createdBy,
                agentId: 'conversation-agent', // This is used for message attribution, not agent lookup
              }
            );
          } catch (error) {
            console.error('Failed to generate agent response:', error);
          }
        });
      } catch (error) {
        console.error('Error triggering agent response:', error);
        // Don't fail the message saving if agent response fails
      }
    }

    return savedMessage;
  }

  // Tool Call methods
  async findAllToolCalls(): Promise<ToolCall[]> {
    return this.toolCallsRepository.find({
      where: { isDeleted: false },
      order: { createdAt: 'DESC' },
    });
  }

  async findToolCallById(id: string): Promise<ToolCall> {
    const toolCall = await this.toolCallsRepository.findOne({
      where: { id, isDeleted: false },
    });

    if (!toolCall) {
      throw new NotFoundException(`ToolCall with ID ${id} not found`);
    }

    return toolCall;
  }

  async findToolCallsByConversationId(conversationId: string): Promise<ToolCall[]> {
    return this.toolCallsRepository.find({
      where: { conversationId, isDeleted: false },
      order: { createdAt: 'ASC' },
    });
  }

  async createToolCall(createToolCallDto: Partial<ToolCall> & { userId?: string | number }): Promise<ToolCall> {
    // Extract userId and map it to the correct fields
    const { userId, ...toolCallData } = createToolCallDto;

    // Create the tool call with proper field mapping
    const toolCall = this.toolCallsRepository.create({
      ...toolCallData,
      createdBy: userId?.toString(),
    });

    return this.toolCallsRepository.save(toolCall);
  }

  async updateToolCall(id: string, updateToolCallDto: Partial<ToolCall>): Promise<ToolCall> {
    const toolCall = await this.findToolCallById(id);
    Object.assign(toolCall, updateToolCallDto);
    return this.toolCallsRepository.save(toolCall);
  }

  async removeToolCall(id: string): Promise<void> {
    const toolCall = await this.findToolCallById(id);
    toolCall.isDeleted = true;
    await this.toolCallsRepository.save(toolCall);
  }
}
